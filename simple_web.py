"""
简化版AutoGen编程工作流网页应用
"""

import streamlit as st
import asyncio
import os
from dotenv import load_dotenv

# 页面配置
st.set_page_config(
    page_title="AutoGen编程工作流",
    page_icon="🤖",
    layout="wide"
)

def main():
    """主函数"""
    st.title("🤖 AutoGen编程工作流系统")
    st.markdown("---")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查配置
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    ai_provider = os.getenv("AI_PROVIDER", "deepseek")
    
    # 显示配置状态
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.info(f"🔧 AI提供商: {ai_provider}")
    
    with col2:
        st.info("🧠 模型: deepseek-chat")
    
    with col3:
        if deepseek_key:
            st.success("🔑 API密钥: 已设置")
        else:
            st.error("🔑 API密钥: 未设置")
    
    st.markdown("---")
    
    # 检查配置
    if not deepseek_key:
        st.error("❌ 请先配置DeepSeek API密钥！")
        st.info("💡 请在.env文件中设置 DEEPSEEK_API_KEY")
        return
    
    # 侧边栏
    st.sidebar.title("🛠️ 配置选项")
    
    workflow_mode = st.sidebar.selectbox(
        "工作流模式",
        ["sequential", "selector"],
        help="sequential: 按固定顺序执行\nselector: 智能选择"
    )
    
    # 示例需求
    examples = [
        "自定义需求",
        "创建斐波那契数列函数",
        "实现简单计算器",
        "编写邮箱验证函数"
    ]
    
    selected_example = st.sidebar.selectbox("选择示例", examples)
    
    # 主内容
    st.title("📝 编程需求输入")
    
    if selected_example == "自定义需求":
        requirement = st.text_area(
            "请描述您的编程需求：",
            height=150,
            placeholder="例如：创建一个Python函数来计算斐波那契数列..."
        )
    else:
        st.info(f"选中示例: {selected_example}")
        requirement = f"请{selected_example}，要求包含完整的文档、错误处理和使用示例。"
        
        if st.checkbox("自定义修改"):
            requirement = st.text_area("修改需求：", value=requirement, height=100)
    
    # 执行按钮
    if st.button("🚀 开始执行工作流", type="primary"):
        if not requirement.strip():
            st.error("❌ 请输入编程需求！")
            return
        
        st.success("✅ 工作流配置正确！")
        st.info("🔄 在实际部署中，这里会执行完整的三智能体协作流程")
        
        # 模拟工作流步骤
        with st.expander("📝 CodeWriter - 代码编写", expanded=True):
            st.write("正在根据需求编写初始代码...")
            st.code("""
def fibonacci(n):
    \"\"\"计算斐波那契数列的第n项\"\"\"
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n-1) + fibonacci(n-2)
            """, language="python")
        
        with st.expander("🔍 CodeReviewer - 代码审查"):
            st.write("正在审查代码质量...")
            st.warning("建议：递归实现效率较低，建议添加缓存或使用迭代方式")
        
        with st.expander("⚡ CodeOptimizer - 代码优化"):
            st.write("正在根据建议优化代码...")
            st.code("""
def fibonacci(n):
    \"\"\"
    计算斐波那契数列的第n项
    使用动态规划优化性能
    \"\"\"
    if not isinstance(n, int) or n < 0:
        raise ValueError("输入必须是非负整数")
    
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return b

# 使用示例
print(fibonacci(10))  # 输出: 55
            """, language="python")
        
        st.success("🎉 工作流执行完成！")
    
    # 帮助信息
    st.sidebar.markdown("---")
    st.sidebar.title("❓ 帮助")
    
    with st.sidebar.expander("如何使用"):
        st.write("""
        1. 确保API密钥已配置
        2. 选择工作流模式
        3. 输入编程需求
        4. 点击执行按钮
        5. 查看三个智能体的协作结果
        """)
    
    with st.sidebar.expander("智能体介绍"):
        st.write("""
        **CodeWriter**: 编写初始代码
        **CodeReviewer**: 审查代码质量
        **CodeOptimizer**: 优化代码
        """)

if __name__ == "__main__":
    main()
