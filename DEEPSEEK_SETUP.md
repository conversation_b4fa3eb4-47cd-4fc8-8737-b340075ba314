# DeepSeek配置指南

本项目现在支持使用DeepSeek API，这是一个高性价比的AI模型服务。

## 🚀 为什么选择DeepSeek？

- **💰 成本效益**: 相比OpenAI更具性价比
- **🌏 本地化**: 更好的中文支持
- **⚡ 性能**: 优秀的代码生成和理解能力
- **🔌 兼容性**: 完全兼容OpenAI API格式

## 📋 配置步骤

### 1. 获取DeepSeek API密钥

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥备用

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置以下配置：
```bash
# 选择使用DeepSeek
AI_PROVIDER=deepseek

# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

### 3. 验证配置

运行配置验证：
```bash
python -c "from config import Config; Config.print_config()"
```

应该看到类似输出：
```
🔧 当前配置:
  AI提供商: deepseek
  模型: deepseek-chat
  API密钥: 已设置
  API端点: https://api.deepseek.com
  工作流模式: sequential
  最大消息数: 15
```

## 🎯 快速开始

配置完成后，可以立即开始使用：

```bash
# 快速演示
python quick_start.py --demo

# 交互式体验
python main.py

# 运行示例
python demo.py --hello
```

## 🔧 高级配置

### 模型选择

DeepSeek提供多个模型，可以在 `.env` 文件中修改：
```bash
# 标准聊天模型（推荐）
DEEPSEEK_MODEL=deepseek-chat

# 代码专用模型
DEEPSEEK_MODEL=deepseek-coder

# 数学推理模型
DEEPSEEK_MODEL=deepseek-math
```

### 性能调优

在 `config.py` 中可以调整智能体的温度参数：
```python
AGENT_CONFIGS = {
    "code_writer": {
        "temperature": 0.7,  # 创造性
    },
    "code_reviewer": {
        "temperature": 0.3,  # 严格性
    },
    "code_optimizer": {
        "temperature": 0.5,  # 平衡
    }
}
```

## 🔄 切换AI提供商

### 切换到DeepSeek
```bash
# 在.env文件中设置
AI_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_key_here
```

### 切换到OpenAI
```bash
# 在.env文件中设置
AI_PROVIDER=openai
OPENAI_API_KEY=your_key_here
```

### 同时配置两个提供商
```bash
# 可以同时配置，通过AI_PROVIDER选择使用哪个
AI_PROVIDER=deepseek

# OpenAI配置
OPENAI_API_KEY=your_openai_key
OPENAI_MODEL=gpt-4o-mini

# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_key
DEEPSEEK_MODEL=deepseek-chat
```

## 💡 使用技巧

### 1. 中文编程任务
DeepSeek对中文理解更好，可以直接用中文描述需求：
```
需求：创建一个中文分词函数，支持jieba分词
```

### 2. 代码注释
DeepSeek生成的代码注释更符合中文开发者习惯

### 3. 错误处理
DeepSeek在错误处理方面表现优秀，生成的代码更健壮

## 🐛 常见问题

### Q: API密钥无效
**A**: 检查密钥是否正确复制，确保没有多余的空格

### Q: 网络连接问题
**A**: 确保网络可以访问 `https://api.deepseek.com`

### Q: 模型响应慢
**A**: DeepSeek服务器可能在高峰期响应较慢，可以稍后重试

### Q: 如何查看API使用量
**A**: 登录DeepSeek控制台查看API调用统计

## 📊 成本对比

| 提供商 | 模型 | 输入价格 | 输出价格 | 性价比 |
|--------|------|----------|----------|--------|
| OpenAI | gpt-4o-mini | $0.15/1M tokens | $0.60/1M tokens | 中等 |
| DeepSeek | deepseek-chat | $0.07/1M tokens | $0.28/1M tokens | 高 |

*价格仅供参考，请以官方最新价格为准*

## 🎉 开始使用

现在您已经配置好DeepSeek，可以享受高性价比的AI编程助手了！

```bash
# 立即开始
python quick_start.py --demo
```

如有问题，请参考项目的 `README.md` 或提交Issue。
