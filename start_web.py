"""
启动网页版AutoGen编程工作流的脚本
"""

import subprocess
import sys
import os

def start_streamlit():
    """启动Streamlit应用"""
    try:
        print("🚀 启动AutoGen编程工作流网页版...")
        print("📍 应用将在浏览器中自动打开")
        print("🔗 如果没有自动打开，请访问: http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "web_app.py", "--server.headless", "false"]
        subprocess.run(cmd, cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请尝试手动运行: python -m streamlit run web_app.py")

if __name__ == "__main__":
    start_streamlit()
