# AutoGen编程工作流 - 网页版使用指南

## 🌐 网页版特性

- **用户友好界面**: 基于Streamlit构建的现代化Web界面
- **实时交互**: 支持实时输入和结果展示
- **可视化工作流**: 清晰展示三个智能体的协作过程
- **示例模板**: 内置多个编程任务示例
- **配置管理**: 可视化的配置状态和选项

## 🚀 启动方法

### 方法1: 使用启动脚本（推荐）
```bash
python start_web.py
```

### 方法2: 直接使用Streamlit命令
```bash
streamlit run web_app.py
```

### 方法3: 使用简化版（测试用）
```bash
streamlit run simple_web.py
```

### 方法4: 手动指定端口
```bash
streamlit run web_app.py --server.port 8501
```

## 📱 访问应用

启动成功后，应用将在以下地址可用：
- **本地访问**: http://localhost:8501
- **网络访问**: http://your-ip:8501

## 🎯 使用步骤

### 1. 配置检查
- 确保DeepSeek API密钥已在.env文件中配置
- 检查页面顶部的配置状态指示器

### 2. 选择工作模式
在左侧边栏选择：
- **Sequential**: 按固定顺序执行（推荐新手）
- **Selector**: 智能选择下一个智能体

### 3. 输入编程需求
- **自定义需求**: 在文本框中详细描述您的编程需求
- **示例需求**: 从预定义示例中选择

### 4. 执行工作流
- 点击"🚀 开始执行工作流"按钮
- 观看三个智能体的协作过程：
  - 📝 **CodeWriter**: 编写初始代码
  - 🔍 **CodeReviewer**: 审查代码质量
  - ⚡ **CodeOptimizer**: 优化代码

### 5. 查看结果
- 查看智能体对话历史
- 获取最终优化的代码
- 复制或下载结果

## 🛠️ 界面功能

### 主要区域
- **头部状态栏**: 显示AI提供商、模型和API密钥状态
- **侧边栏**: 配置选项和帮助信息
- **主内容区**: 需求输入和结果展示
- **进度显示**: 实时显示工作流执行状态

### 侧边栏功能
- **工作流模式选择**: Sequential/Selector
- **示例需求选择**: 快速选择常见编程任务
- **系统信息**: 当前配置概览
- **帮助文档**: 使用说明和智能体介绍

## 📋 预定义示例

1. **斐波那契数列**: 创建高效的斐波那契数列计算函数
2. **待办事项管理器**: 实现完整的任务管理系统
3. **邮箱验证器**: 编写邮箱格式验证功能
4. **简单计算器**: 创建支持多种运算的计算器
5. **文件处理工具**: 实现文件读写和处理功能

## 🔧 高级配置

### 环境变量配置
在.env文件中设置：
```bash
# 选择AI提供商
AI_PROVIDER=deepseek

# DeepSeek配置
DEEPSEEK_API_KEY=your_api_key_here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

### Streamlit配置
创建`.streamlit/config.toml`文件：
```toml
[server]
port = 8501
headless = false
enableCORS = false

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
```

## 🐛 故障排除

### 常见问题

**Q: 页面无法加载**
- 检查Streamlit是否正确启动
- 确认端口8501没有被占用
- 尝试刷新浏览器或清除缓存

**Q: API密钥错误**
- 检查.env文件中的DEEPSEEK_API_KEY
- 确认API密钥格式正确
- 验证API密钥是否有效

**Q: 工作流执行失败**
- 检查网络连接
- 确认DeepSeek API服务可用
- 查看浏览器控制台的错误信息

**Q: 页面响应慢**
- 检查网络连接速度
- 确认DeepSeek API响应时间
- 尝试使用更简单的需求测试

### 调试模式
启动时添加调试参数：
```bash
streamlit run web_app.py --logger.level debug
```

## 🔒 安全注意事项

1. **API密钥保护**: 不要在公共环境中暴露API密钥
2. **网络访问**: 如需外网访问，请配置适当的安全措施
3. **数据隐私**: 输入的代码需求会发送到DeepSeek API

## 📱 移动端支持

网页版支持移动设备访问：
- 响应式设计适配手机和平板
- 触摸友好的界面元素
- 优化的移动端布局

## 🎨 自定义主题

可以通过修改CSS样式自定义界面：
- 编辑`web_app.py`中的CSS样式
- 创建自定义主题配置
- 调整颜色和布局

## 📊 使用统计

网页版会显示：
- 工作流执行次数
- 平均响应时间
- 成功率统计

## 🔄 更新和维护

定期更新：
- 检查AutoGen库的新版本
- 更新Streamlit到最新版本
- 关注DeepSeek API的变化

## 💡 使用技巧

1. **需求描述**: 越详细的需求描述，生成的代码质量越高
2. **示例选择**: 新手建议从预定义示例开始
3. **结果保存**: 及时保存满意的代码结果
4. **迭代优化**: 可以基于结果进一步提出优化需求

## 🎉 开始使用

现在您可以通过以下命令启动网页版：

```bash
# 启动完整版
python start_web.py

# 或启动简化版（用于测试）
streamlit run simple_web.py
```

然后在浏览器中访问 http://localhost:8501 开始使用！
