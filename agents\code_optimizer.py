"""
代码优化智能体

负责根据原始代码和审查建议生成优化后的代码。
"""

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient


class CodeOptimizerAgent:
    """代码优化智能体
    
    该智能体专门负责根据原始代码和审查建议生成优化后的代码。
    具备以下能力：
    - 理解审查建议并应用到代码中
    - 优化代码性能和可读性
    - 修复潜在的bug和安全问题
    - 改进代码结构和设计
    - 添加必要的错误处理和测试
    """
    
    def __init__(self, model_client: ChatCompletionClient):
        """初始化代码优化智能体
        
        Args:
            model_client: 聊天完成模型客户端
        """
        self.model_client = model_client
        
        # 定义系统消息，指导智能体的行为
        self.system_message = """你是一个专业的代码优化智能体。你的任务是根据原始代码和审查建议，生成优化后的高质量代码。

你的优化重点：
1. 应用审查建议中的所有合理建议
2. 修复发现的bug和潜在问题
3. 优化代码性能和内存使用
4. 改进代码结构和可读性
5. 增强错误处理和异常管理
6. 添加必要的注释和文档
7. 遵循编程最佳实践
8. 确保代码的可测试性和可维护性

优化输出格式：
1. 优化说明：简要说明主要的优化内容
2. 优化后的代码：完整的优化后代码
3. 改进点总结：列出具体的改进点
4. 使用示例：提供使用示例（如果适用）
5. 注意事项：使用时需要注意的事项

请确保优化后的代码：
- 功能完整且正确
- 性能更优
- 更易维护
- 遵循最佳实践
- 包含适当的错误处理"""

        # 创建AssistantAgent实例
        self.agent = AssistantAgent(
            name="CodeOptimizer",
            model_client=self.model_client,
            system_message=self.system_message,
            description="专业的代码优化智能体，负责根据审查建议优化代码"
        )
    
    def get_agent(self) -> AssistantAgent:
        """获取AssistantAgent实例
        
        Returns:
            AssistantAgent: 配置好的智能体实例
        """
        return self.agent
    
    async def optimize_code(self, original_code: str, review_feedback: str, requirement: str = "") -> str:
        """根据审查建议优化代码
        
        Args:
            original_code: 原始代码
            review_feedback: 审查反馈和建议
            requirement: 原始需求描述（可选）
            
        Returns:
            str: 优化后的代码和说明
        """
        task = f"""请根据以下信息优化代码：

{'原始需求：' + requirement if requirement else ''}

原始代码：
```
{original_code}
```

审查建议：
{review_feedback}

请提供：
1. 优化说明：简要说明主要的优化内容
2. 优化后的完整代码
3. 改进点总结：列出具体的改进点
4. 使用示例（如果适用）
5. 注意事项

请确保优化后的代码功能完整、性能更优、更易维护。"""

        response = await self.agent.run(task=task)
        return response.messages[-1].content if response.messages else "代码优化失败"
