"""
AutoGen编程工作流配置文件

包含系统配置参数和默认设置。
"""

import os
from typing import Dict, Any


class Config:
    """配置管理类"""

    # AI服务提供商选择
    AI_PROVIDER = os.getenv("AI_PROVIDER", "deepseek")  # openai 或 deepseek

    # OpenAI配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL")

    # DeepSeek配置
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")
    
    # 工作流配置
    DEFAULT_WORKFLOW_MODE = "sequential"  # sequential 或 selector
    MAX_MESSAGES = 15  # 最大消息数
    
    # 智能体配置
    AGENT_CONFIGS = {
        "code_writer": {
            "name": "CodeWriter",
            "description": "专业的代码编写智能体，负责根据需求编写高质量代码",
            "temperature": 0.7,  # 创造性设置
        },
        "code_reviewer": {
            "name": "CodeReviewer", 
            "description": "专业的代码审查智能体，负责审查代码质量并提出改进建议",
            "temperature": 0.3,  # 更严格的审查
        },
        "code_optimizer": {
            "name": "CodeOptimizer",
            "description": "专业的代码优化智能体，负责根据审查建议优化代码",
            "temperature": 0.5,  # 平衡创造性和准确性
        }
    }
    
    # 终止条件配置
    TERMINATION_KEYWORDS = ["COMPLETE", "FINISHED", "DONE"]
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @classmethod
    def validate(cls) -> bool:
        """验证配置是否有效"""
        if cls.AI_PROVIDER == "openai":
            if not cls.OPENAI_API_KEY:
                print("❌ 错误: OPENAI_API_KEY 未设置")
                return False
        elif cls.AI_PROVIDER == "deepseek":
            if not cls.DEEPSEEK_API_KEY:
                print("❌ 错误: DEEPSEEK_API_KEY 未设置")
                return False
        else:
            print(f"❌ 错误: 不支持的AI提供商 {cls.AI_PROVIDER}")
            return False

        if cls.DEFAULT_WORKFLOW_MODE not in ["sequential", "selector"]:
            print(f"❌ 错误: 无效的工作流模式 {cls.DEFAULT_WORKFLOW_MODE}")
            return False

        return True
    
    @classmethod
    def get_model_config(cls) -> Dict[str, Any]:
        """获取模型配置"""
        if cls.AI_PROVIDER == "openai":
            config = {
                "model": cls.OPENAI_MODEL,
                "api_key": cls.OPENAI_API_KEY,
            }
            if cls.OPENAI_BASE_URL:
                config["base_url"] = cls.OPENAI_BASE_URL
        elif cls.AI_PROVIDER == "deepseek":
            from autogen_core.models import ModelInfo, ModelFamily

            # 为DeepSeek创建模型信息
            model_info = ModelInfo(
                family=ModelFamily.GPT_4,  # DeepSeek兼容OpenAI GPT-4 API
                vision=False,
                function_calling=True,
                json_output=True,
                structured_output=True  # 添加缺失的字段
            )

            config = {
                "model": cls.DEEPSEEK_MODEL,
                "api_key": cls.DEEPSEEK_API_KEY,
                "base_url": cls.DEEPSEEK_BASE_URL,
                "model_info": model_info
            }
        else:
            raise ValueError(f"不支持的AI提供商: {cls.AI_PROVIDER}")

        return config
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("🔧 当前配置:")
        print(f"  AI提供商: {cls.AI_PROVIDER}")

        if cls.AI_PROVIDER == "openai":
            print(f"  模型: {cls.OPENAI_MODEL}")
            print(f"  API密钥: {'已设置' if cls.OPENAI_API_KEY else '未设置'}")
            if cls.OPENAI_BASE_URL:
                print(f"  API端点: {cls.OPENAI_BASE_URL}")
        elif cls.AI_PROVIDER == "deepseek":
            print(f"  模型: {cls.DEEPSEEK_MODEL}")
            print(f"  API密钥: {'已设置' if cls.DEEPSEEK_API_KEY else '未设置'}")
            print(f"  API端点: {cls.DEEPSEEK_BASE_URL}")

        print(f"  工作流模式: {cls.DEFAULT_WORKFLOW_MODE}")
        print(f"  最大消息数: {cls.MAX_MESSAGES}")


# 预定义的示例需求
EXAMPLE_REQUIREMENTS = [
    {
        "name": "斐波那契数列",
        "description": "创建一个Python函数来计算斐波那契数列",
        "details": """要求：
1. 支持递归和迭代两种实现方式
2. 包含输入验证
3. 添加适当的文档字符串
4. 考虑性能优化"""
    },
    {
        "name": "待办事项管理器",
        "description": "实现一个简单的待办事项管理类",
        "details": """要求：
1. 支持添加、删除、标记完成待办事项
2. 支持按优先级和状态筛选
3. 支持保存到文件和从文件加载
4. 包含适当的错误处理
5. 提供清晰的API接口"""
    },
    {
        "name": "邮箱验证器",
        "description": "编写一个函数来验证邮箱地址格式",
        "details": """要求：
1. 使用正则表达式进行验证
2. 支持常见的邮箱格式
3. 返回详细的验证结果
4. 包含测试用例
5. 考虑国际化域名"""
    },
    {
        "name": "简单计算器",
        "description": "创建一个简单的计算器类",
        "details": """要求：
1. 支持基本的四则运算
2. 支持括号和运算优先级
3. 包含历史记录功能
4. 支持科学计算函数（sin, cos, log等）
5. 提供友好的错误处理"""
    },
    {
        "name": "文件处理工具",
        "description": "实现一个文件读写工具函数集",
        "details": """要求：
1. 支持多种文件格式（txt, json, csv）
2. 包含文件备份功能
3. 支持批量处理
4. 添加进度显示
5. 包含错误恢复机制"""
    }
]


# 系统提示模板
SYSTEM_PROMPTS = {
    "code_writer": """你是一个专业的代码编写智能体。你的任务是根据用户的需求描述编写高质量的代码。

你的职责：
1. 仔细分析用户的需求描述
2. 选择最适合的编程语言和框架
3. 编写清晰、可读、可维护的代码
4. 添加适当的注释和文档字符串
5. 遵循编程最佳实践和代码规范
6. 考虑错误处理和边界情况

编写代码时请遵循以下原则：
- 代码结构清晰，逻辑简洁
- 变量和函数命名有意义
- 适当的注释和文档
- 考虑性能和可扩展性
- 包含必要的错误处理

请在代码前后添加简要说明，解释你的设计思路和实现方案。""",

    "code_reviewer": """你是一个专业的代码审查智能体。你的任务是仔细审查提供的代码，并提出具体的改进建议。

你的审查重点：
1. 代码质量和可读性
2. 编程规范和最佳实践
3. 潜在的bug和错误处理
4. 性能优化机会
5. 安全性问题
6. 代码结构和设计模式
7. 注释和文档完整性
8. 测试覆盖率考虑

审查输出格式：
1. 总体评价：对代码的整体质量进行评估
2. 具体问题：列出发现的具体问题，按优先级排序
3. 改进建议：提供详细的改进建议和解决方案
4. 最佳实践：指出可以应用的最佳实践
5. 安全考虑：指出潜在的安全风险
6. 性能建议：提出性能优化建议

请保持客观、建设性的态度，提供具体可行的建议。""",

    "code_optimizer": """你是一个专业的代码优化智能体。你的任务是根据原始代码和审查建议，生成优化后的高质量代码。

你的优化重点：
1. 应用审查建议中的所有合理建议
2. 修复发现的bug和潜在问题
3. 优化代码性能和内存使用
4. 改进代码结构和可读性
5. 增强错误处理和异常管理
6. 添加必要的注释和文档
7. 遵循编程最佳实践
8. 确保代码的可测试性和可维护性

优化输出格式：
1. 优化说明：简要说明主要的优化内容
2. 优化后的代码：完整的优化后代码
3. 改进点总结：列出具体的改进点
4. 使用示例：提供使用示例（如果适用）
5. 注意事项：使用时需要注意的事项

请确保优化后的代码：
- 功能完整且正确
- 性能更优
- 更易维护
- 遵循最佳实践
- 包含适当的错误处理"""
}
