"""
AutoGen编程工作流测试模块

包含对工作流系统各个组件的单元测试和集成测试。
"""

import unittest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
import sys
sys.path.append('..')

from agents import CodeWriterAgent, CodeReviewerAgent, CodeOptimizerAgent
from workflow import ProgrammingWorkflow


class MockModelClient:
    """模拟模型客户端用于测试"""
    
    def __init__(self, mock_response="测试响应"):
        self.mock_response = mock_response
        self.call_count = 0
    
    async def create(self, messages, **kwargs):
        """模拟创建聊天完成"""
        self.call_count += 1
        
        # 模拟响应对象
        class MockResponse:
            def __init__(self, content):
                self.content = content
        
        return MockResponse(self.mock_response)
    
    async def close(self):
        """模拟关闭连接"""
        pass


class TestCodeWriterAgent(unittest.TestCase):
    """测试代码编写智能体"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockModelClient("生成的测试代码")
        self.agent = CodeWriterAgent(self.mock_client)
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertIsNotNone(self.agent.agent)
        self.assertEqual(self.agent.agent.name, "CodeWriter")
        self.assertIn("代码编写智能体", self.agent.agent.description)
    
    async def test_write_code(self):
        """测试代码编写功能"""
        requirement = "创建一个简单的Hello World函数"
        
        # 模拟agent.run方法
        with patch.object(self.agent.agent, 'run', new_callable=AsyncMock) as mock_run:
            # 模拟返回结果
            mock_result = Mock()
            mock_result.messages = [Mock(content="生成的代码内容")]
            mock_run.return_value = mock_result
            
            result = await self.agent.write_code(requirement)
            
            self.assertEqual(result, "生成的代码内容")
            mock_run.assert_called_once()


class TestCodeReviewerAgent(unittest.TestCase):
    """测试代码审查智能体"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockModelClient("审查报告")
        self.agent = CodeReviewerAgent(self.mock_client)
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertIsNotNone(self.agent.agent)
        self.assertEqual(self.agent.agent.name, "CodeReviewer")
        self.assertIn("代码审查智能体", self.agent.agent.description)
    
    async def test_review_code(self):
        """测试代码审查功能"""
        code = "def hello(): print('Hello World')"
        requirement = "创建Hello World函数"
        
        # 模拟agent.run方法
        with patch.object(self.agent.agent, 'run', new_callable=AsyncMock) as mock_run:
            # 模拟返回结果
            mock_result = Mock()
            mock_result.messages = [Mock(content="审查建议内容")]
            mock_run.return_value = mock_result
            
            result = await self.agent.review_code(code, requirement)
            
            self.assertEqual(result, "审查建议内容")
            mock_run.assert_called_once()


class TestCodeOptimizerAgent(unittest.TestCase):
    """测试代码优化智能体"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockModelClient("优化后的代码")
        self.agent = CodeOptimizerAgent(self.mock_client)
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertIsNotNone(self.agent.agent)
        self.assertEqual(self.agent.agent.name, "CodeOptimizer")
        self.assertIn("代码优化智能体", self.agent.agent.description)
    
    async def test_optimize_code(self):
        """测试代码优化功能"""
        original_code = "def hello(): print('Hello World')"
        review_feedback = "建议添加文档字符串"
        requirement = "创建Hello World函数"
        
        # 模拟agent.run方法
        with patch.object(self.agent.agent, 'run', new_callable=AsyncMock) as mock_run:
            # 模拟返回结果
            mock_result = Mock()
            mock_result.messages = [Mock(content="优化后的代码内容")]
            mock_run.return_value = mock_result
            
            result = await self.agent.optimize_code(original_code, review_feedback, requirement)
            
            self.assertEqual(result, "优化后的代码内容")
            mock_run.assert_called_once()


class TestProgrammingWorkflow(unittest.TestCase):
    """测试编程工作流"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockModelClient()
    
    def test_workflow_initialization_sequential(self):
        """测试顺序工作流初始化"""
        workflow = ProgrammingWorkflow(self.mock_client, "sequential")
        
        self.assertEqual(workflow.workflow_mode, "sequential")
        self.assertIsNotNone(workflow.code_writer)
        self.assertIsNotNone(workflow.code_reviewer)
        self.assertIsNotNone(workflow.code_optimizer)
        self.assertIsNotNone(workflow.team)
    
    def test_workflow_initialization_selector(self):
        """测试选择器工作流初始化"""
        workflow = ProgrammingWorkflow(self.mock_client, "selector")
        
        self.assertEqual(workflow.workflow_mode, "selector")
        self.assertIsNotNone(workflow.team)
    
    def test_invalid_workflow_mode(self):
        """测试无效工作流模式"""
        with self.assertRaises(ValueError):
            ProgrammingWorkflow(self.mock_client, "invalid_mode")
    
    async def test_workflow_run(self):
        """测试工作流运行"""
        workflow = ProgrammingWorkflow(self.mock_client, "sequential")
        
        # 模拟team.run方法
        with patch.object(workflow.team, 'run', new_callable=AsyncMock) as mock_run:
            mock_result = Mock()
            mock_run.return_value = mock_result
            
            requirement = "创建一个测试函数"
            result = await workflow.run_workflow(requirement)
            
            self.assertEqual(result, mock_result)
            mock_run.assert_called_once()


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockModelClient()
    
    async def test_full_workflow_integration(self):
        """测试完整工作流集成"""
        workflow = ProgrammingWorkflow(self.mock_client, "sequential")
        
        # 模拟整个工作流程
        with patch.object(workflow.team, 'run', new_callable=AsyncMock) as mock_run:
            # 模拟工作流结果
            mock_result = Mock()
            mock_result.messages = [
                Mock(content="代码编写完成", source="CodeWriter"),
                Mock(content="代码审查完成", source="CodeReviewer"),
                Mock(content="代码优化完成", source="CodeOptimizer")
            ]
            mock_run.return_value = mock_result
            
            requirement = "创建一个计算器函数"
            result = await workflow.run_workflow(requirement)
            
            self.assertIsNotNone(result)
            mock_run.assert_called_once()


def run_async_test(test_func):
    """运行异步测试的辅助函数"""
    def wrapper(self):
        asyncio.run(test_func(self))
    return wrapper


# 为异步测试方法添加装饰器
TestCodeWriterAgent.test_write_code = run_async_test(TestCodeWriterAgent.test_write_code)
TestCodeReviewerAgent.test_review_code = run_async_test(TestCodeReviewerAgent.test_review_code)
TestCodeOptimizerAgent.test_optimize_code = run_async_test(TestCodeOptimizerAgent.test_optimize_code)
TestProgrammingWorkflow.test_workflow_run = run_async_test(TestProgrammingWorkflow.test_workflow_run)
TestIntegration.test_full_workflow_integration = run_async_test(TestIntegration.test_full_workflow_integration)


if __name__ == "__main__":
    # 运行测试
    print("🧪 运行AutoGen编程工作流测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestCodeWriterAgent))
    test_suite.addTest(unittest.makeSuite(TestCodeReviewerAgent))
    test_suite.addTest(unittest.makeSuite(TestCodeOptimizerAgent))
    test_suite.addTest(unittest.makeSuite(TestProgrammingWorkflow))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有测试通过!")
    else:
        print(f"❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
