# AutoGen编程工作流 - 快速开始指南

欢迎使用AutoGen编程工作流系统！这个指南将帮助您快速上手。

## 🚀 5分钟快速体验

### 第一步：环境准备
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置API密钥
cp .env.example .env
# 编辑 .env 文件，设置你的 OPENAI_API_KEY
```

### 第二步：快速演示
```bash
# 运行快速演示（推荐）
python quick_start.py --demo

# 或者运行交互式演示
python demo.py
```

### 第三步：体验完整功能
```bash
# 运行主程序
python main.py
```

## 📋 使用场景示例

### 1. 代码生成场景
**输入需求**：创建一个Python函数来计算斐波那契数列
**工作流程**：
1. CodeWriter 编写初始实现
2. CodeReviewer 审查代码质量
3. CodeOptimizer 根据建议优化代码

### 2. 代码重构场景
**输入需求**：优化现有的排序算法实现
**工作流程**：
1. CodeWriter 分析现有代码
2. CodeReviewer 识别改进点
3. CodeOptimizer 实现优化方案

### 3. 学习辅助场景
**输入需求**：学习设计模式，实现观察者模式
**工作流程**：
1. CodeWriter 提供基础实现
2. CodeReviewer 解释设计原理
3. CodeOptimizer 展示最佳实践

## 🎯 推荐的学习路径

### 新手用户
1. 运行 `python quick_start.py --demo` 观看演示
2. 尝试 `python demo.py --hello` 简单示例
3. 使用 `python main.py` 体验完整功能

### 进阶用户
1. 查看 `examples/example_workflow.py` 了解高级用法
2. 阅读 `config.py` 了解配置选项
3. 运行 `python tests/test_workflow.py` 查看测试

### 开发者
1. 研究 `agents/` 目录下的智能体实现
2. 了解 `workflow/` 目录下的编排逻辑
3. 参考文档扩展自定义功能

## 🔧 配置说明

### 基础配置
在 `.env` 文件中设置：
```bash
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4o-mini  # 可选，默认模型
```

### 高级配置
在 `config.py` 中可以调整：
- 智能体行为参数
- 工作流终止条件
- 日志级别设置

## 🎮 交互式命令

### 快速启动命令
```bash
python quick_start.py          # 交互式快速启动
python quick_start.py --demo   # 快速演示
python quick_start.py --examples  # 查看示例
```

### 演示命令
```bash
python demo.py                 # 交互式演示选择
python demo.py --hello         # Hello World演示
python demo.py --fibonacci     # 斐波那契演示
python demo.py --batch         # 批量演示
```

### 示例命令
```bash
python examples/example_workflow.py --fibonacci  # 斐波那契示例
python examples/example_workflow.py --todo       # 待办事项示例
python examples/example_workflow.py --all        # 所有示例
```

## 🐛 常见问题解决

### Q: 提示API密钥错误
**A**: 检查 `.env` 文件中的 `OPENAI_API_KEY` 是否正确设置

### Q: 网络连接超时
**A**: 确保网络可以访问 OpenAI API，可能需要配置代理

### Q: 模型不可用
**A**: 尝试更换其他可用模型，如 `gpt-3.5-turbo`

### Q: 依赖包安装失败
**A**: 尝试使用 `pip install --upgrade pip` 更新pip后重新安装

## 📚 进一步学习

### 官方文档
- [AutoGen官方文档](https://github.com/microsoft/autogen)
- [OpenAI API文档](https://platform.openai.com/docs)

### 项目文件说明
- `main.py` - 主程序入口
- `agents/` - 智能体定义
- `workflow/` - 工作流编排
- `examples/` - 使用示例
- `tests/` - 测试用例
- `config.py` - 配置管理

### 扩展开发
参考 `README.md` 中的扩展开发章节，了解如何：
- 添加新的智能体
- 自定义工作流
- 集成其他AI模型

## 🎉 开始您的编程工作流之旅！

现在您已经准备好开始使用AutoGen编程工作流系统了。建议从快速演示开始，然后逐步探索更多功能。

如果遇到问题，请查看项目的 `README.md` 文件或提交Issue。

祝您使用愉快！🚀
