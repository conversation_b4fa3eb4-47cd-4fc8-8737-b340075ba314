"""
AutoGen编程工作流 - Streamlit网页版

提供用户友好的Web界面来使用AutoGen编程工作流系统
"""

import streamlit as st
import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import Config, EXAMPLE_REQUIREMENTS
from workflow import ProgrammingWorkflow
import time


# 页面配置
st.set_page_config(
    page_title="AutoGen编程工作流",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .info-box {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    .workflow-step {
        background-color: #f8f9fa;
        padding: 1rem;
        border-left: 4px solid #007bff;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def load_config():
    """加载配置"""
    load_dotenv()
    return Config.validate()


def display_header():
    """显示页面头部"""
    st.markdown('<h1 class="main-header">🤖 AutoGen编程工作流系统</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # 显示配置状态
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if Config.AI_PROVIDER == "deepseek":
            st.success(f"🔧 AI提供商: {Config.AI_PROVIDER}")
        else:
            st.info(f"🔧 AI提供商: {Config.AI_PROVIDER}")
    
    with col2:
        model = Config.DEEPSEEK_MODEL if Config.AI_PROVIDER == "deepseek" else Config.OPENAI_MODEL
        st.info(f"🧠 模型: {model}")
    
    with col3:
        api_key_status = "已设置" if (Config.DEEPSEEK_API_KEY if Config.AI_PROVIDER == "deepseek" else Config.OPENAI_API_KEY) else "未设置"
        if api_key_status == "已设置":
            st.success(f"🔑 API密钥: {api_key_status}")
        else:
            st.error(f"🔑 API密钥: {api_key_status}")


def display_sidebar():
    """显示侧边栏"""
    st.sidebar.title("🛠️ 配置选项")
    
    # 工作流模式选择
    workflow_mode = st.sidebar.selectbox(
        "选择工作流模式",
        ["sequential", "selector"],
        index=0,
        help="sequential: 按固定顺序执行\nselector: 智能选择下一个agent"
    )
    
    # 示例需求选择
    st.sidebar.title("📋 示例需求")
    example_names = [req["name"] for req in EXAMPLE_REQUIREMENTS]
    selected_example = st.sidebar.selectbox(
        "选择示例需求",
        ["自定义需求"] + example_names,
        index=0
    )
    
    # 显示配置信息
    st.sidebar.title("ℹ️ 系统信息")
    st.sidebar.info(f"""
    **AI提供商**: {Config.AI_PROVIDER}
    **模型**: {Config.DEEPSEEK_MODEL if Config.AI_PROVIDER == "deepseek" else Config.OPENAI_MODEL}
    **工作流模式**: {workflow_mode}
    """)
    
    # 帮助信息
    st.sidebar.title("❓ 帮助")
    with st.sidebar.expander("如何使用"):
        st.write("""
        1. 确保API密钥已配置
        2. 选择工作流模式
        3. 输入编程需求或选择示例
        4. 点击"开始执行"
        5. 查看三个智能体的协作结果
        """)
    
    with st.sidebar.expander("智能体介绍"):
        st.write("""
        **CodeWriter**: 根据需求编写初始代码
        
        **CodeReviewer**: 审查代码质量并提出建议
        
        **CodeOptimizer**: 根据建议优化代码
        """)
    
    return workflow_mode, selected_example


async def run_workflow_async(requirement: str, workflow_mode: str):
    """异步运行工作流"""
    try:
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(**Config.get_model_config())
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, workflow_mode)
        
        # 运行工作流
        result = await workflow.run_workflow(requirement)
        
        # 清理资源
        await workflow.close()
        
        return result, None
        
    except Exception as e:
        return None, str(e)


def run_workflow_sync(requirement: str, workflow_mode: str):
    """同步运行工作流的包装器"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result, error = loop.run_until_complete(run_workflow_async(requirement, workflow_mode))
        loop.close()
        return result, error
    except Exception as e:
        return None, str(e)


def main():
    """主函数"""
    # 加载配置
    config_valid = load_config()
    
    # 显示头部
    display_header()
    
    # 检查配置
    if not config_valid:
        st.error("❌ 配置验证失败！请检查.env文件中的API密钥设置。")
        st.info("💡 请参考项目中的DEEPSEEK_SETUP.md文件进行配置。")
        return
    
    # 显示侧边栏
    workflow_mode, selected_example = display_sidebar()
    
    # 主内容区域
    st.title("📝 编程需求输入")
    
    # 需求输入
    if selected_example == "自定义需求":
        requirement = st.text_area(
            "请描述您的编程需求：",
            height=150,
            placeholder="例如：创建一个Python函数来计算斐波那契数列...",
            help="请详细描述您的编程需求，包括功能要求、技术栈偏好等"
        )
    else:
        # 显示选中的示例
        example = next(req for req in EXAMPLE_REQUIREMENTS if req["name"] == selected_example)
        st.info(f"**选中示例**: {example['name']}")
        st.write(f"**描述**: {example['description']}")
        
        with st.expander("查看详细要求"):
            st.write(example['details'])
        
        requirement = f"{example['description']}\n\n{example['details']}"
        
        # 允许用户修改
        if st.checkbox("自定义修改此示例"):
            requirement = st.text_area(
                "修改需求：",
                value=requirement,
                height=150
            )
    
    # 执行按钮
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 开始执行工作流", type="primary", use_container_width=True):
            if not requirement.strip():
                st.error("❌ 请输入编程需求！")
                return
            
            # 显示执行状态
            st.markdown("---")
            st.subheader("🔄 工作流执行中...")
            
            # 创建进度条和状态显示
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 步骤显示
            step1 = st.empty()
            step2 = st.empty()
            step3 = st.empty()
            
            try:
                # 步骤1: 初始化
                progress_bar.progress(10)
                status_text.text("🔧 初始化工作流...")
                time.sleep(1)
                
                # 步骤2: 执行工作流
                progress_bar.progress(30)
                status_text.text("🤖 智能体协作中...")
                
                step1.markdown('<div class="workflow-step">📝 <strong>CodeWriter</strong>: 正在编写代码...</div>', unsafe_allow_html=True)
                
                # 运行工作流
                result, error = run_workflow_sync(requirement, workflow_mode)
                
                if error:
                    st.error(f"❌ 执行失败: {error}")
                    return
                
                progress_bar.progress(60)
                step2.markdown('<div class="workflow-step">🔍 <strong>CodeReviewer</strong>: 正在审查代码...</div>', unsafe_allow_html=True)
                time.sleep(1)
                
                progress_bar.progress(90)
                step3.markdown('<div class="workflow-step">⚡ <strong>CodeOptimizer</strong>: 正在优化代码...</div>', unsafe_allow_html=True)
                time.sleep(1)
                
                # 完成
                progress_bar.progress(100)
                status_text.text("✅ 工作流执行完成！")
                
                # 显示结果
                st.markdown("---")
                st.subheader("🎯 执行结果")
                
                if result and hasattr(result, 'messages') and result.messages:
                    # 显示对话历史
                    st.subheader("💬 智能体对话历史")
                    
                    for i, message in enumerate(result.messages):
                        if hasattr(message, 'source') and hasattr(message, 'content'):
                            with st.expander(f"💬 {message.source} - 消息 {i+1}"):
                                st.write(message.content)
                    
                    # 显示最终结果
                    if result.messages:
                        st.subheader("🏆 最终优化结果")
                        final_message = result.messages[-1]
                        st.success("工作流执行成功！")
                        st.markdown(final_message.content)
                else:
                    st.warning("⚠️ 工作流执行完成，但未获取到详细结果。")
                
            except Exception as e:
                st.error(f"❌ 执行过程中出现错误: {str(e)}")
                st.info("💡 请检查网络连接和API密钥配置。")


if __name__ == "__main__":
    main()
