"""
AutoGen编程工作流快速启动脚本

提供简化的接口来快速体验编程工作流功能。
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from workflow import ProgrammingWorkflow
from config import Config, EXAMPLE_REQUIREMENTS


async def quick_demo():
    """快速演示"""
    print("🚀 AutoGen编程工作流 - 快速演示")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 验证配置
    if not Config.validate():
        print("💡 请先配置环境变量，参考.env.example文件")
        return
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 使用第一个示例需求进行演示
        example = EXAMPLE_REQUIREMENTS[0]
        requirement = f"{example['description']}\n\n{example['details']}"
        
        print(f"📋 演示需求: {example['name']}")
        print(f"📝 需求描述: {example['description']}")
        print("\n" + "=" * 60)
        
        # 创建工作流（使用顺序模式）
        workflow = ProgrammingWorkflow(model_client, "sequential")
        
        # 运行工作流
        await workflow.run_workflow_stream(requirement)
        
        print("\n✅ 快速演示完成!")
        print("💡 运行 python main.py 体验完整功能")
        
    except Exception as e:
        print(f"❌ 演示执行出错: {str(e)}")
    finally:
        try:
            await workflow.close()
        except:
            pass


async def simple_workflow(requirement: str, mode: str = "sequential"):
    """简化的工作流接口
    
    Args:
        requirement: 编程需求描述
        mode: 工作流模式 ("sequential" 或 "selector")
    """
    # 加载环境变量
    load_dotenv()
    
    # 验证配置
    if not Config.validate():
        raise ValueError("配置验证失败，请检查环境变量")
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, mode)
        
        # 运行工作流
        result = await workflow.run_workflow(requirement)
        
        return result
        
    finally:
        try:
            await workflow.close()
        except:
            pass


def print_examples():
    """打印示例需求"""
    print("📋 预定义示例需求:")
    print("=" * 60)
    
    for i, example in enumerate(EXAMPLE_REQUIREMENTS, 1):
        print(f"\n{i}. {example['name']}")
        print(f"   描述: {example['description']}")
        print(f"   详情: {example['details'][:100]}...")


async def interactive_quick_start():
    """交互式快速启动"""
    print("🎮 AutoGen编程工作流 - 交互式快速启动")
    print("=" * 60)
    
    # 显示配置信息
    Config.print_config()
    print()
    
    # 验证配置
    if not Config.validate():
        print("💡 请先配置环境变量:")
        print("1. 复制 .env.example 为 .env")
        print("2. 在 .env 文件中设置 OPENAI_API_KEY")
        return
    
    print("✅ 配置验证通过!")
    print()
    
    # 选择操作
    print("请选择操作:")
    print("1. 快速演示 (使用预定义示例)")
    print("2. 自定义需求")
    print("3. 查看示例需求")
    print("4. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await quick_demo()
            break
        elif choice == "2":
            await custom_requirement()
            break
        elif choice == "3":
            print_examples()
            print("\n" + "=" * 60)
            continue
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入1-4")


async def custom_requirement():
    """自定义需求处理"""
    print("\n📝 自定义编程需求")
    print("-" * 40)
    
    # 获取需求描述
    requirement = input("请描述你的编程需求: ").strip()
    
    if not requirement:
        print("❌ 需求描述不能为空")
        return
    
    # 选择工作流模式
    print("\n请选择工作流模式:")
    print("1. 顺序模式 (sequential) - 按固定顺序执行")
    print("2. 智能选择模式 (selector) - 根据上下文智能选择")
    
    while True:
        mode_choice = input("请输入选择 (1/2): ").strip()
        if mode_choice == "1":
            mode = "sequential"
            break
        elif mode_choice == "2":
            mode = "selector"
            break
        else:
            print("❌ 无效选择，请输入1或2")
    
    print(f"\n🚀 开始处理需求: {requirement}")
    print(f"🔄 工作模式: {mode}")
    print("=" * 60)
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, mode)
        
        # 运行工作流
        await workflow.run_workflow_stream(requirement)
        
        print("\n✅ 需求处理完成!")
        
    except Exception as e:
        print(f"❌ 处理出错: {str(e)}")
    finally:
        try:
            await workflow.close()
        except:
            pass


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            # 快速演示
            asyncio.run(quick_demo())
        elif sys.argv[1] == "--examples":
            # 显示示例
            print_examples()
        elif sys.argv[1] == "--help":
            # 显示帮助
            print("AutoGen编程工作流快速启动脚本")
            print("\n可用参数:")
            print("  --demo      运行快速演示")
            print("  --examples  显示示例需求")
            print("  --help      显示此帮助信息")
            print("\n无参数运行将启动交互式模式")
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        # 交互式模式
        asyncio.run(interactive_quick_start())
