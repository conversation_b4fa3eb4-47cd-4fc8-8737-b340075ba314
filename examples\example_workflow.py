"""
AutoGen编程工作流示例

演示如何使用编程工作流系统来完成各种编程任务。
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
import sys
sys.path.append('..')
from workflow import ProgrammingWorkflow
from config import Config


async def example_fibonacci():
    """示例1: 斐波那契数列函数"""
    print("🔢 示例1: 创建斐波那契数列函数")
    print("=" * 60)
    
    requirement = """创建一个Python函数来计算斐波那契数列。
要求：
1. 支持递归和迭代两种实现方式
2. 包含输入验证
3. 添加适当的文档字符串
4. 考虑性能优化"""
    
    return requirement


async def example_todo_manager():
    """示例2: 待办事项管理器"""
    print("📝 示例2: 待办事项管理器")
    print("=" * 60)
    
    requirement = """实现一个简单的待办事项管理类。
要求：
1. 支持添加、删除、标记完成待办事项
2. 支持按优先级和状态筛选
3. 支持保存到文件和从文件加载
4. 包含适当的错误处理
5. 提供清晰的API接口"""
    
    return requirement


async def example_email_validator():
    """示例3: 邮箱验证器"""
    print("📧 示例3: 邮箱地址验证器")
    print("=" * 60)
    
    requirement = """编写一个函数来验证邮箱地址格式。
要求：
1. 使用正则表达式进行验证
2. 支持常见的邮箱格式
3. 返回详细的验证结果
4. 包含测试用例
5. 考虑国际化域名"""
    
    return requirement


async def example_calculator():
    """示例4: 计算器类"""
    print("🧮 示例4: 简单计算器类")
    print("=" * 60)
    
    requirement = """创建一个简单的计算器类。
要求：
1. 支持基本的四则运算
2. 支持括号和运算优先级
3. 包含历史记录功能
4. 支持科学计算函数（sin, cos, log等）
5. 提供友好的错误处理"""
    
    return requirement


async def run_example(example_func, workflow_mode="sequential"):
    """运行单个示例"""
    # 加载环境变量
    load_dotenv()

    # 验证配置
    if not Config.validate():
        print("❌ 配置验证失败")
        print("💡 请检查.env文件中的API密钥设置")
        return

    # 创建模型客户端 (支持OpenAI和DeepSeek)
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 获取示例需求
        requirement = await example_func()
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, workflow_mode)
        
        print(f"🔄 工作模式: {workflow_mode}")
        print(f"📋 需求: {requirement}")
        print("\n" + "=" * 80)
        
        # 运行工作流
        await workflow.run_workflow_stream(requirement)
        
    except Exception as e:
        print(f"❌ 示例执行出错: {str(e)}")
    finally:
        try:
            await workflow.close()
        except:
            pass


async def run_all_examples():
    """运行所有示例"""
    print("🎯 AutoGen编程工作流 - 完整示例演示")
    print("=" * 80)
    
    examples = [
        ("斐波那契数列", example_fibonacci),
        ("待办事项管理器", example_todo_manager),
        ("邮箱验证器", example_email_validator),
        ("计算器类", example_calculator)
    ]
    
    print("📋 可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    # 选择示例
    while True:
        try:
            choice = input(f"\n请选择示例 (1-{len(examples)}) 或 'all' 运行所有示例: ").strip().lower()
            
            if choice == 'all':
                # 运行所有示例
                for name, example_func in examples:
                    print(f"\n🚀 运行示例: {name}")
                    await run_example(example_func)
                    print("\n" + "🔄" * 40 + "\n")
                break
            else:
                choice_num = int(choice)
                if 1 <= choice_num <= len(examples):
                    name, example_func = examples[choice_num - 1]
                    print(f"\n🚀 运行示例: {name}")
                    await run_example(example_func)
                    break
                else:
                    print(f"❌ 请输入1到{len(examples)}之间的数字")
        except ValueError:
            print("❌ 请输入有效的数字或 'all'")


async def interactive_example():
    """交互式示例选择"""
    print("🎮 AutoGen编程工作流 - 交互式示例")
    print("=" * 60)
    
    # 选择工作流模式
    print("📋 请选择工作流模式:")
    print("1. 顺序模式 (sequential)")
    print("2. 智能选择模式 (selector)")
    
    while True:
        mode_choice = input("\n请输入选择 (1/2): ").strip()
        if mode_choice == "1":
            workflow_mode = "sequential"
            break
        elif mode_choice == "2":
            workflow_mode = "selector"
            break
        else:
            print("❌ 无效选择，请输入1或2")
    
    await run_all_examples()


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--fibonacci":
            asyncio.run(run_example(example_fibonacci))
        elif sys.argv[1] == "--todo":
            asyncio.run(run_example(example_todo_manager))
        elif sys.argv[1] == "--email":
            asyncio.run(run_example(example_email_validator))
        elif sys.argv[1] == "--calculator":
            asyncio.run(run_example(example_calculator))
        elif sys.argv[1] == "--all":
            asyncio.run(run_all_examples())
        else:
            print("❌ 未知参数")
            print("💡 可用参数: --fibonacci, --todo, --email, --calculator, --all")
    else:
        # 交互式模式
        asyncio.run(interactive_example())
