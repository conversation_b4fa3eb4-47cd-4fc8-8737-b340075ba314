"""
AutoGen编程工作流主程序

这是一个使用Microsoft AutoGen框架构建的多智能体编程工作流系统。
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from workflow import ProgrammingWorkflow
from config import Config


async def main():
    """主程序入口"""
    # 加载环境变量
    load_dotenv()

    # 验证配置
    if not Config.validate():
        print("💡 提示: 请检查.env文件中的配置")
        print("📋 支持的AI提供商:")
        print("  - OpenAI: 设置 OPENAI_API_KEY")
        print("  - DeepSeek: 设置 DEEPSEEK_API_KEY (推荐)")
        return

    print("🤖 AutoGen编程工作流系统")
    print("=" * 50)

    # 显示当前配置
    Config.print_config()

    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 选择工作流模式
        print("\n📋 请选择工作流模式:")
        print("1. 顺序模式 (sequential) - 按固定顺序执行")
        print("2. 智能选择模式 (selector) - 根据上下文智能选择")
        
        while True:
            choice = input("\n请输入选择 (1/2): ").strip()
            if choice == "1":
                workflow_mode = "sequential"
                break
            elif choice == "2":
                workflow_mode = "selector"
                break
            else:
                print("❌ 无效选择，请输入1或2")
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, workflow_mode)
        
        print(f"\n✅ 工作流已创建 (模式: {workflow_mode})")
        
        # 获取用户需求
        print("\n📝 请描述你的编程需求:")
        print("💡 示例: 创建一个Python函数来计算斐波那契数列")
        print("💡 示例: 实现一个简单的待办事项管理类")
        print("💡 示例: 编写一个网页爬虫来获取新闻标题")
        
        requirement = input("\n需求描述: ").strip()
        
        if not requirement:
            print("❌ 需求描述不能为空")
            return
        
        print(f"\n🎯 开始处理需求: {requirement}")
        
        # 运行工作流
        await workflow.run_workflow_stream(requirement)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
    finally:
        # 清理资源
        try:
            await workflow.close()
        except:
            pass
        print("\n👋 程序结束")


def interactive_demo():
    """交互式演示模式"""
    print("🎮 AutoGen编程工作流 - 交互式演示")
    print("=" * 50)
    
    # 预定义的示例需求
    examples = [
        "创建一个Python函数来计算斐波那契数列",
        "实现一个简单的待办事项管理类",
        "编写一个函数来验证邮箱地址格式",
        "创建一个简单的计算器类",
        "实现一个文件读写工具函数"
    ]
    
    print("📋 预定义示例需求:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example}")
    
    print(f"{len(examples) + 1}. 自定义需求")
    
    while True:
        try:
            choice = int(input(f"\n请选择示例 (1-{len(examples) + 1}): "))
            if 1 <= choice <= len(examples):
                requirement = examples[choice - 1]
                break
            elif choice == len(examples) + 1:
                requirement = input("请输入自定义需求: ").strip()
                if requirement:
                    break
                else:
                    print("❌ 需求不能为空")
            else:
                print(f"❌ 请输入1到{len(examples) + 1}之间的数字")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    return requirement


if __name__ == "__main__":
    # 检查是否有命令行参数
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        # 演示模式
        requirement = interactive_demo()
        if requirement:
            # 使用演示需求运行主程序
            asyncio.run(main())
    else:
        # 正常模式
        asyncio.run(main())
