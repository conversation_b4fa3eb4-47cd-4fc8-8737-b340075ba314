"""
DeepSeek配置测试脚本

用于验证DeepSeek API配置是否正确。
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import Config


async def test_deepseek_connection():
    """测试DeepSeek连接"""
    print("🧪 DeepSeek配置测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置
    Config.print_config()
    print()
    
    # 验证配置
    if not Config.validate():
        print("❌ 配置验证失败")
        return False
    
    if Config.AI_PROVIDER != "deepseek":
        print("⚠️  当前未使用DeepSeek，请在.env文件中设置 AI_PROVIDER=deepseek")
        return False
    
    print("✅ 配置验证通过")
    print()
    
    try:
        # 创建客户端
        print("🔗 创建DeepSeek客户端...")
        model_client = OpenAIChatCompletionClient(**Config.get_model_config())
        
        # 测试简单对话
        print("💬 测试API连接...")
        from autogen_core.models import UserMessage, SystemMessage
        
        messages = [
            SystemMessage(content="你是一个有用的AI助手。"),
            UserMessage(content="请简单介绍一下你自己，用一句话回答。", source="user")
        ]
        
        response = await model_client.create(messages)
        
        print("✅ API连接成功!")
        print(f"📝 响应内容: {response.content}")
        print()
        
        # 测试代码生成
        print("🔧 测试代码生成能力...")
        code_messages = [
            SystemMessage(content="你是一个专业的Python程序员。"),
            UserMessage(content="写一个简单的Hello World函数", source="user")
        ]
        
        code_response = await model_client.create(code_messages)
        print("✅ 代码生成测试成功!")
        print(f"📝 生成的代码:\n{code_response.content}")
        
        await model_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print()
        print("💡 可能的解决方案:")
        print("1. 检查DEEPSEEK_API_KEY是否正确")
        print("2. 确认网络可以访问 https://api.deepseek.com")
        print("3. 验证API密钥是否有效且有足够余额")
        return False


async def test_workflow_with_deepseek():
    """测试完整工作流"""
    print("\n🚀 测试完整编程工作流...")
    print("-" * 50)
    
    try:
        from workflow import ProgrammingWorkflow
        
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(**Config.get_model_config())
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, "sequential")
        
        # 简单测试需求
        requirement = "创建一个简单的加法函数，接受两个数字参数并返回它们的和"
        
        print(f"📋 测试需求: {requirement}")
        print()
        
        # 运行工作流
        result = await workflow.run_workflow(requirement)
        
        print("✅ 工作流测试成功!")
        await workflow.close()
        return True
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {str(e)}")
        return False


def print_setup_guide():
    """打印设置指南"""
    print("\n📋 DeepSeek设置指南:")
    print("-" * 30)
    print("1. 访问 https://platform.deepseek.com/ 注册账号")
    print("2. 获取API密钥")
    print("3. 在.env文件中设置:")
    print("   AI_PROVIDER=deepseek")
    print("   DEEPSEEK_API_KEY=your_api_key_here")
    print("4. 运行此测试脚本验证配置")
    print()
    print("📖 详细指南请参考: DEEPSEEK_SETUP.md")


async def main():
    """主测试函数"""
    print("🎯 AutoGen + DeepSeek 配置测试")
    print("=" * 60)
    
    # 基础连接测试
    connection_ok = await test_deepseek_connection()
    
    if connection_ok:
        # 工作流测试
        workflow_ok = await test_workflow_with_deepseek()
        
        if workflow_ok:
            print("\n🎉 所有测试通过! DeepSeek配置正确!")
            print("💡 现在可以运行: python quick_start.py --demo")
        else:
            print("\n⚠️  基础连接正常，但工作流测试失败")
    else:
        print("\n❌ 基础连接测试失败")
        print_setup_guide()


if __name__ == "__main__":
    asyncio.run(main())

