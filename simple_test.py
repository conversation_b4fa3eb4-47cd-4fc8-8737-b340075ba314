"""
简单的工作流测试

测试DeepSeek配置和基本的智能体功能
"""

import asyncio
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import Config
from agents import CodeWriterAgent


async def test_single_agent():
    """测试单个智能体"""
    print("🧪 测试单个智能体功能")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 验证配置
    if not Config.validate():
        print("❌ 配置验证失败")
        return
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        # 创建代码编写智能体
        code_writer = CodeWriterAgent(model_client)
        
        # 测试简单需求
        requirement = "创建一个简单的加法函数，接受两个数字参数并返回它们的和"
        
        print(f"📋 测试需求: {requirement}")
        print("\n🔄 智能体工作中...")
        
        # 调用智能体
        result = await code_writer.write_code(requirement)
        
        print("\n✅ 智能体响应:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        await model_client.close()
        print("\n🎉 单个智能体测试成功!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        try:
            await model_client.close()
        except:
            pass


async def test_simple_workflow():
    """测试简化的工作流"""
    print("\n🚀 测试简化工作流")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        from agents import CodeWriterAgent, CodeReviewerAgent, CodeOptimizerAgent
        
        # 创建智能体
        writer = CodeWriterAgent(model_client)
        reviewer = CodeReviewerAgent(model_client)
        optimizer = CodeOptimizerAgent(model_client)
        
        requirement = "创建一个计算圆面积的函数"
        
        print(f"📋 需求: {requirement}")
        
        # 步骤1: 编写代码
        print("\n🔧 步骤1: 编写代码...")
        code = await writer.write_code(requirement)
        print("✅ 代码编写完成")
        
        # 步骤2: 审查代码
        print("\n🔍 步骤2: 审查代码...")
        review = await reviewer.review_code(code, requirement)
        print("✅ 代码审查完成")
        
        # 步骤3: 优化代码
        print("\n⚡ 步骤3: 优化代码...")
        optimized = await optimizer.optimize_code(code, review, requirement)
        print("✅ 代码优化完成")
        
        print("\n🎯 最终结果:")
        print("=" * 60)
        print(optimized)
        print("=" * 60)
        
        await model_client.close()
        print("\n🎉 简化工作流测试成功!")
        
    except Exception as e:
        print(f"\n❌ 工作流测试失败: {str(e)}")
        try:
            await model_client.close()
        except:
            pass


async def main():
    """主测试函数"""
    print("🎯 AutoGen + DeepSeek 简单测试")
    print("=" * 60)
    
    # 显示配置
    Config.print_config()
    print()
    
    # 测试单个智能体
    await test_single_agent()
    
    # 测试简化工作流
    await test_simple_workflow()
    
    print("\n🏁 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
