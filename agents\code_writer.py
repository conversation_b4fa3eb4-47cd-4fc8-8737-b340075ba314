"""
代码编写智能体

负责根据用户需求编写初始代码，支持多种编程语言和框架。
"""

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient


class CodeWriterAgent:
    """代码编写智能体
    
    该智能体专门负责根据用户的需求描述编写高质量的代码。
    具备以下能力：
    - 理解自然语言需求描述
    - 选择合适的编程语言和框架
    - 编写结构清晰、注释完整的代码
    - 遵循最佳实践和编码规范
    """
    
    def __init__(self, model_client: ChatCompletionClient):
        """初始化代码编写智能体
        
        Args:
            model_client: 聊天完成模型客户端
        """
        self.model_client = model_client
        
        # 定义系统消息，指导智能体的行为
        self.system_message = """你是一个专业的代码编写智能体。你的任务是根据用户的需求描述编写高质量的代码。

你的职责：
1. 仔细分析用户的需求描述
2. 选择最适合的编程语言和框架
3. 编写清晰、可读、可维护的代码
4. 添加适当的注释和文档字符串
5. 遵循编程最佳实践和代码规范
6. 考虑错误处理和边界情况

编写代码时请遵循以下原则：
- 代码结构清晰，逻辑简洁
- 变量和函数命名有意义
- 适当的注释和文档
- 考虑性能和可扩展性
- 包含必要的错误处理

请在代码前后添加简要说明，解释你的设计思路和实现方案。"""

        # 创建AssistantAgent实例
        self.agent = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            system_message=self.system_message,
            description="专业的代码编写智能体，负责根据需求编写高质量代码"
        )
    
    def get_agent(self) -> AssistantAgent:
        """获取AssistantAgent实例
        
        Returns:
            AssistantAgent: 配置好的智能体实例
        """
        return self.agent
    
    async def write_code(self, requirement: str) -> str:
        """根据需求编写代码
        
        Args:
            requirement: 用户需求描述
            
        Returns:
            str: 生成的代码和说明
        """
        task = f"""请根据以下需求编写代码：

需求描述：
{requirement}

请提供：
1. 代码实现
2. 简要的设计说明
3. 使用示例（如果适用）
4. 注意事项"""

        response = await self.agent.run(task=task)
        return response.messages[-1].content if response.messages else "代码生成失败"
