# AutoGen编程工作流

这是一个使用Microsoft AutoGen框架构建的多智能体编程工作流系统。该系统包含三个专门的智能体：

1. **CodeWriterAgent** - 代码编写智能体，负责根据用户需求编写初始代码
2. **CodeReviewerAgent** - 代码审查智能体，负责审查代码并提出具体的修改建议  
3. **CodeOptimizerAgent** - 代码优化智能体，负责根据原始代码和审查建议生成优化后的代码

## 功能特性

- 🤖 基于AutoGen最新框架构建的多智能体协作系统
- 📝 智能代码生成和优化
- 🔍 专业的代码审查和建议
- 🔄 迭代式代码改进流程
- 💬 自然语言交互界面
- 🎯 支持多种编程语言和框架
- ⚡ 两种工作流模式：顺序执行和智能选择
- 🧪 完整的测试覆盖和示例代码
- 🔧 灵活的配置和扩展能力
- 💰 支持DeepSeek和OpenAI，高性价比选择

## 工作流程

```mermaid
graph TD
    A[用户输入需求] --> B[CodeWriter 编写代码]
    B --> C[CodeReviewer 审查代码]
    C --> D[CodeOptimizer 优化代码]
    D --> E[输出最终结果]

    C --> F{需要重新编写?}
    F -->|是| B
    F -->|否| D

    D --> G{需要再次审查?}
    G -->|是| C
    G -->|否| E
```

### 智能体角色

1. **CodeWriter (代码编写智能体)**
   - 分析用户需求
   - 选择合适的技术栈
   - 编写结构清晰的代码
   - 添加注释和文档

2. **CodeReviewer (代码审查智能体)**
   - 检查代码质量
   - 识别潜在问题
   - 评估性能和安全性
   - 提出改进建议

3. **CodeOptimizer (代码优化智能体)**
   - 应用审查建议
   - 优化代码性能
   - 改进代码结构
   - 增强错误处理

## 安装和配置

1. 安装依赖包：
```bash
pip install -r requirements.txt
```

2. 配置AI服务（推荐使用DeepSeek）：

### 选项A: 使用DeepSeek（推荐）
```bash
cp .env.example .env
# 编辑.env文件，设置：
# AI_PROVIDER=deepseek
# DEEPSEEK_API_KEY=your_deepseek_api_key
```

### 选项B: 使用OpenAI
```bash
cp .env.example .env
# 编辑.env文件，设置：
# AI_PROVIDER=openai
# OPENAI_API_KEY=your_openai_api_key
```

📋 **DeepSeek配置详细指南**: 请参考 [DEEPSEEK_SETUP.md](DEEPSEEK_SETUP.md)

## 使用方法

### 快速开始

1. **快速演示**（推荐新用户）：
```bash
python quick_start.py --demo
```

2. **交互式快速启动**：
```bash
python quick_start.py
```

3. **查看示例需求**：
```bash
python quick_start.py --examples
```

4. **测试DeepSeek配置**：
```bash
python test_deepseek.py
```

### 完整功能

1. **运行主程序**：
```bash
python main.py
```

2. **运行特定示例**：
```bash
# 斐波那契数列示例
python examples/example_workflow.py --fibonacci

# 待办事项管理器示例
python examples/example_workflow.py --todo

# 邮箱验证器示例
python examples/example_workflow.py --email

# 计算器类示例
python examples/example_workflow.py --calculator

# 运行所有示例
python examples/example_workflow.py --all
```

3. **运行测试**：
```bash
python tests/test_workflow.py
```

## 项目结构

```
.
├── agents/                 # 智能体定义
│   ├── __init__.py
│   ├── code_writer.py     # 代码编写智能体
│   ├── code_reviewer.py   # 代码审查智能体
│   └── code_optimizer.py  # 代码优化智能体
├── workflow/              # 工作流编排
│   ├── __init__.py
│   └── programming_workflow.py
├── examples/              # 示例代码
│   ├── __init__.py
│   └── example_workflow.py
├── tests/                 # 测试用例
│   ├── __init__.py
│   └── test_workflow.py
├── main.py               # 主程序入口
├── requirements.txt      # 依赖包
├── .env.example         # 环境变量示例
└── README.md           # 项目说明
```

## 工作流模式

### 1. 顺序模式 (Sequential)
- 按固定顺序执行：CodeWriter → CodeReviewer → CodeOptimizer
- 适合标准的代码开发流程
- 执行过程可预测，适合新用户

### 2. 智能选择模式 (Selector)
- 根据对话上下文智能选择下一个智能体
- 更灵活的执行流程
- 适合复杂的编程任务

## 配置选项

在 `config.py` 中可以自定义：
- 模型参数（温度、最大token等）
- 智能体行为配置
- 终止条件设置
- 日志级别

## 扩展开发

### 添加新的智能体
1. 在 `agents/` 目录下创建新的智能体类
2. 继承 `AssistantAgent` 并定义系统消息
3. 在 `agents/__init__.py` 中导出
4. 在工作流中集成

### 自定义工作流
1. 在 `workflow/` 目录下创建新的工作流类
2. 使用 AutoGen 的团队协作功能
3. 定义自定义的终止条件和选择逻辑

## 故障排除

### 常见问题
1. **API密钥错误**：检查 `.env` 文件中的 `OPENAI_API_KEY`
2. **网络连接问题**：确保网络可以访问 OpenAI API
3. **模型不可用**：尝试更换其他可用的模型
4. **依赖包问题**：运行 `pip install -r requirements.txt`

### 调试模式
设置环境变量 `LOG_LEVEL=DEBUG` 获取详细日志

## 贡献指南

欢迎贡献代码！请遵循以下步骤：
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License
