"""
代码审查智能体

负责审查代码质量，提出改进建议和最佳实践建议。
"""

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient


class CodeReviewerAgent:
    """代码审查智能体
    
    该智能体专门负责审查代码质量并提出改进建议。
    具备以下能力：
    - 检查代码质量和规范性
    - 识别潜在的bug和安全问题
    - 评估代码性能和可维护性
    - 提出具体的改进建议
    - 检查最佳实践的遵循情况
    """
    
    def __init__(self, model_client: ChatCompletionClient):
        """初始化代码审查智能体
        
        Args:
            model_client: 聊天完成模型客户端
        """
        self.model_client = model_client
        
        # 定义系统消息，指导智能体的行为
        self.system_message = """你是一个专业的代码审查智能体。你的任务是仔细审查提供的代码，并提出具体的改进建议。

你的审查重点：
1. 代码质量和可读性
2. 编程规范和最佳实践
3. 潜在的bug和错误处理
4. 性能优化机会
5. 安全性问题
6. 代码结构和设计模式
7. 注释和文档完整性
8. 测试覆盖率考虑

审查输出格式：
1. 总体评价：对代码的整体质量进行评估
2. 具体问题：列出发现的具体问题，按优先级排序
3. 改进建议：提供详细的改进建议和解决方案
4. 最佳实践：指出可以应用的最佳实践
5. 安全考虑：指出潜在的安全风险
6. 性能建议：提出性能优化建议

请保持客观、建设性的态度，提供具体可行的建议。"""

        # 创建AssistantAgent实例
        self.agent = AssistantAgent(
            name="CodeReviewer",
            model_client=self.model_client,
            system_message=self.system_message,
            description="专业的代码审查智能体，负责审查代码质量并提出改进建议"
        )
    
    def get_agent(self) -> AssistantAgent:
        """获取AssistantAgent实例
        
        Returns:
            AssistantAgent: 配置好的智能体实例
        """
        return self.agent
    
    async def review_code(self, code: str, requirement: str = "") -> str:
        """审查代码并提出建议
        
        Args:
            code: 要审查的代码
            requirement: 原始需求描述（可选）
            
        Returns:
            str: 审查报告和改进建议
        """
        task = f"""请审查以下代码并提出改进建议：

{'原始需求：' + requirement if requirement else ''}

代码内容：
```
{code}
```

请提供详细的审查报告，包括：
1. 总体评价
2. 发现的具体问题
3. 改进建议
4. 最佳实践建议
5. 安全性考虑
6. 性能优化建议

请确保建议具体可行，并按重要性排序。"""

        response = await self.agent.run(task=task)
        return response.messages[-1].content if response.messages else "代码审查失败"
