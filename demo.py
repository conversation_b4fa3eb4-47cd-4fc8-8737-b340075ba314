"""
AutoGen编程工作流演示脚本

展示完整的编程工作流功能，包括代码编写、审查和优化的全过程。
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from workflow import ProgrammingWorkflow
from config import Config


async def demo_fibonacci_workflow():
    """演示斐波那契数列编程工作流"""
    print("🔢 演示：斐波那契数列编程工作流")
    print("=" * 80)
    
    requirement = """创建一个Python函数来计算斐波那契数列。

要求：
1. 支持递归和迭代两种实现方式
2. 包含输入验证（处理负数、非整数等）
3. 添加完整的文档字符串和类型注解
4. 考虑性能优化（如缓存机制）
5. 包含使用示例和测试用例"""
    
    return requirement


async def demo_simple_workflow():
    """演示简单的Hello World工作流"""
    print("👋 演示：Hello World编程工作流")
    print("=" * 80)
    
    requirement = """创建一个简单的Hello World程序。

要求：
1. 创建一个函数，接受姓名参数
2. 返回个性化的问候语
3. 支持多语言问候（中文、英文）
4. 包含输入验证
5. 添加适当的文档和示例"""
    
    return requirement


async def run_demo_workflow(requirement: str, workflow_mode: str = "sequential"):
    """运行演示工作流"""
    # 加载环境变量
    load_dotenv()
    
    # 验证配置
    if not Config.validate():
        print("❌ 配置验证失败")
        print("💡 请确保已设置 OPENAI_API_KEY 环境变量")
        return
    
    # 创建模型客户端
    model_client = OpenAIChatCompletionClient(**Config.get_model_config())
    
    try:
        print(f"🔧 使用模型: {Config.OPENAI_MODEL}")
        print(f"🔄 工作模式: {workflow_mode}")
        print(f"📋 需求描述:\n{requirement}")
        print("\n" + "🚀" * 40)
        print("开始执行工作流...")
        print("🚀" * 40 + "\n")
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_client, workflow_mode)
        
        # 运行工作流
        await workflow.run_workflow_stream(requirement)
        
        print("\n" + "✅" * 40)
        print("工作流执行完成!")
        print("✅" * 40)
        
    except Exception as e:
        print(f"\n❌ 演示执行出错: {str(e)}")
        print("💡 请检查网络连接和API密钥设置")
    finally:
        try:
            await workflow.close()
        except:
            pass


async def interactive_demo():
    """交互式演示选择"""
    print("🎮 AutoGen编程工作流 - 交互式演示")
    print("=" * 80)
    
    # 显示配置
    Config.print_config()
    print()
    
    # 验证配置
    if not Config.validate():
        print("💡 配置设置指南:")
        print("1. 复制 .env.example 为 .env")
        print("2. 编辑 .env 文件，设置你的 OPENAI_API_KEY")
        print("3. 可选：设置 OPENAI_MODEL（默认使用 gpt-4o-mini）")
        return
    
    print("✅ 配置验证通过!\n")
    
    # 选择演示
    demos = [
        ("简单Hello World", demo_simple_workflow),
        ("斐波那契数列", demo_fibonacci_workflow),
    ]
    
    print("📋 可用演示:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    
    # 选择工作流模式
    print(f"\n{len(demos) + 1}. 自定义需求")
    print(f"{len(demos) + 2}. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择演示 (1-{len(demos) + 2}): ").strip()
            choice_num = int(choice)
            
            if 1 <= choice_num <= len(demos):
                # 运行预定义演示
                name, demo_func = demos[choice_num - 1]
                requirement = await demo_func()
                
                # 选择工作流模式
                mode = select_workflow_mode()
                if mode:
                    await run_demo_workflow(requirement, mode)
                break
                
            elif choice_num == len(demos) + 1:
                # 自定义需求
                await custom_demo()
                break
                
            elif choice_num == len(demos) + 2:
                # 退出
                print("👋 再见!")
                break
                
            else:
                print(f"❌ 请输入1到{len(demos) + 2}之间的数字")
                
        except ValueError:
            print("❌ 请输入有效的数字")


def select_workflow_mode():
    """选择工作流模式"""
    print("\n🔄 请选择工作流模式:")
    print("1. 顺序模式 (sequential) - 按固定顺序执行")
    print("2. 智能选择模式 (selector) - 根据上下文智能选择")
    
    while True:
        mode_choice = input("请输入选择 (1/2): ").strip()
        if mode_choice == "1":
            return "sequential"
        elif mode_choice == "2":
            return "selector"
        else:
            print("❌ 无效选择，请输入1或2")


async def custom_demo():
    """自定义演示"""
    print("\n📝 自定义编程需求演示")
    print("-" * 50)
    
    requirement = input("请描述你的编程需求:\n").strip()
    
    if not requirement:
        print("❌ 需求描述不能为空")
        return
    
    # 选择工作流模式
    mode = select_workflow_mode()
    if mode:
        await run_demo_workflow(requirement, mode)


async def batch_demo():
    """批量演示所有预定义示例"""
    print("🎯 批量演示所有预定义示例")
    print("=" * 80)
    
    demos = [
        ("Hello World", demo_simple_workflow),
        ("斐波那契数列", demo_fibonacci_workflow),
    ]
    
    for i, (name, demo_func) in enumerate(demos, 1):
        print(f"\n📍 演示 {i}/{len(demos)}: {name}")
        print("-" * 60)
        
        requirement = await demo_func()
        await run_demo_workflow(requirement, "sequential")
        
        if i < len(demos):
            print("\n⏳ 准备下一个演示...")
            await asyncio.sleep(2)  # 短暂暂停
    
    print("\n🎉 所有演示完成!")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--hello":
            # Hello World演示
            asyncio.run(run_demo_workflow(asyncio.run(demo_simple_workflow())))
        elif sys.argv[1] == "--fibonacci":
            # 斐波那契演示
            asyncio.run(run_demo_workflow(asyncio.run(demo_fibonacci_workflow())))
        elif sys.argv[1] == "--batch":
            # 批量演示
            asyncio.run(batch_demo())
        elif sys.argv[1] == "--help":
            # 帮助信息
            print("AutoGen编程工作流演示脚本")
            print("\n可用参数:")
            print("  --hello      运行Hello World演示")
            print("  --fibonacci  运行斐波那契数列演示")
            print("  --batch      批量运行所有演示")
            print("  --help       显示此帮助信息")
            print("\n无参数运行将启动交互式演示")
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        # 交互式演示
        asyncio.run(interactive_demo())
