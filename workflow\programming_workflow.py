"""
编程工作流编排模块

使用AutoGen的团队协作功能编排代码编写、审查和优化的完整工作流。
"""

import asyncio
from typing import Optional
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_core.models import Chat<PERSON>ompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient

from agents import CodeWriterAgent, CodeReviewerAgent, CodeOptimizerAgent


class ProgrammingWorkflow:
    """编程工作流管理器
    
    该类负责编排代码编写、审查和优化的完整工作流程。
    支持两种工作模式：
    1. 顺序模式（RoundRobinGroupChat）：按固定顺序执行
    2. 智能选择模式（SelectorGroupChat）：根据上下文智能选择下一个智能体
    """
    
    def __init__(self, model_client: ChatCompletionClient, workflow_mode: str = "sequential"):
        """初始化编程工作流
        
        Args:
            model_client: 聊天完成模型客户端
            workflow_mode: 工作流模式，"sequential"或"selector"
        """
        self.model_client = model_client
        self.workflow_mode = workflow_mode
        
        # 初始化三个智能体
        self.code_writer = CodeWriterAgent(model_client)
        self.code_reviewer = CodeReviewerAgent(model_client)
        self.code_optimizer = CodeOptimizerAgent(model_client)
        
        # 设置终止条件
        self.termination_condition = self._create_termination_condition()
        
        # 创建团队
        self.team = self._create_team()
    
    def _create_termination_condition(self):
        """创建终止条件"""
        # 当提到"COMPLETE"或达到最大消息数时终止
        text_termination = TextMentionTermination("COMPLETE")
        max_messages_termination = MaxMessageTermination(max_messages=15)
        return text_termination | max_messages_termination
    
    def _create_team(self):
        """创建智能体团队"""
        agents = [
            self.code_writer.get_agent(),
            self.code_reviewer.get_agent(),
            self.code_optimizer.get_agent()
        ]
        
        if self.workflow_mode == "sequential":
            # 顺序模式：按固定顺序执行
            return RoundRobinGroupChat(
                participants=agents,
                termination_condition=self.termination_condition
            )
        elif self.workflow_mode == "selector":
            # 智能选择模式：根据上下文选择下一个智能体
            return SelectorGroupChat(
                participants=agents,
                model_client=self.model_client,
                termination_condition=self.termination_condition,
                selector_prompt=self._get_selector_prompt()
            )
        else:
            raise ValueError(f"不支持的工作流模式: {workflow_mode}")
    
    def _get_selector_prompt(self) -> str:
        """获取智能体选择提示"""
        return """选择下一个智能体来处理任务。

智能体角色：
- CodeWriter: 负责编写初始代码，适合处理新的编程需求
- CodeReviewer: 负责审查代码质量，适合在代码编写完成后进行审查
- CodeOptimizer: 负责优化代码，适合在收到审查建议后进行代码改进

选择规则：
1. 如果是新的编程任务，选择CodeWriter
2. 如果有代码需要审查，选择CodeReviewer  
3. 如果有审查建议需要应用，选择CodeOptimizer
4. 如果任务已完成，让当前智能体说"COMPLETE"

当前对话上下文：
{history}

请从 {participants} 中选择最合适的智能体来处理下一步任务。"""
    
    async def run_workflow(self, requirement: str) -> str:
        """运行编程工作流
        
        Args:
            requirement: 编程需求描述
            
        Returns:
            str: 工作流执行结果
        """
        task = f"""编程任务：{requirement}

工作流程：
1. CodeWriter将根据需求编写初始代码
2. CodeReviewer将审查代码并提出改进建议
3. CodeOptimizer将根据建议优化代码
4. 完成后请说"COMPLETE"

请开始执行任务。"""

        print(f"🚀 开始执行编程工作流...")
        print(f"📋 任务需求: {requirement}")
        print(f"🔄 工作模式: {self.workflow_mode}")
        print("=" * 80)
        
        # 运行团队工作流
        result = await self.team.run(task=task)
        
        print("=" * 80)
        print("✅ 工作流执行完成!")
        
        return result
    
    async def run_workflow_stream(self, requirement: str):
        """流式运行编程工作流
        
        Args:
            requirement: 编程需求描述
        """
        task = f"""编程任务：{requirement}

工作流程：
1. CodeWriter将根据需求编写初始代码
2. CodeReviewer将审查代码并提出改进建议  
3. CodeOptimizer将根据建议优化代码
4. 完成后请说"COMPLETE"

请开始执行任务。"""

        print(f"🚀 开始执行编程工作流...")
        print(f"📋 任务需求: {requirement}")
        print(f"🔄 工作模式: {self.workflow_mode}")
        print("=" * 80)
        
        # 流式运行团队工作流
        await Console(self.team.run_stream(task=task))
        
        print("=" * 80)
        print("✅ 工作流执行完成!")
    
    async def close(self):
        """关闭资源"""
        if hasattr(self.model_client, 'close'):
            await self.model_client.close()
